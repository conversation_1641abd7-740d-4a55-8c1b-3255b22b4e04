
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { memo, useCallback, useMemo, useRef } from "react";

import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import ReportEditor from "~/components/editor";
import { useReplay } from "~/core/replay";
import { useMessage, useStore } from "~/core/store";
import { cn } from "~/lib/utils";

// 优化：添加memo以防止不必要的重新渲染
export const ResearchReportBlock = memo(function ResearchReportBlock({
  className,
  messageId,
  editing,
}: {
  className?: string;
  researchId: string;
  messageId: string;
  editing: boolean;
}) {
  // 1. 所有hooks必须在组件顶部声明，不能有条件调用
  const message = useMessage(messageId);
  const { isReplay } = useReplay();
  const contentRef = useRef<HTMLDivElement>(null);

  // 2. 优化：使用useCallback并减少依赖项以防止不必要的重新创建
  const handleMarkdownChange = useCallback(
      (markdown: string) => {
        if (!message?.id) return; // 提前返回但不影响 Hook 调用

        // 将状态更新逻辑移到 Hook 之外
        const newState = useStore.getState();
        const newMessages = new Map(newState.messages);
        const currentMessage = newMessages.get(message.id);

        if (currentMessage) {
          newMessages.set(message.id, {
            ...currentMessage,
            content: markdown,
          });
          useStore.setState({ messages: newMessages }); // 确保 Hook 调用在顶层
        }
      },
      [message?.id]
  );

  // 3. 优化：缓存计算结果，减少重复计算
  const isCompleted = useMemo(() =>
    Boolean(message && !message.isStreaming && message.content),
    [message]
  );

  const shouldShowEditor = useMemo(() =>
    !isReplay && isCompleted && editing,
    [isReplay, isCompleted, editing]
  );

  const hasContent = useMemo(() =>
    Boolean(message?.content),
    [message?.content]
  );

  const isLargeContent = useMemo(() =>
    hasContent && (message?.content?.length ?? 0) > 50000,
    [hasContent, message?.content?.length]
  );

  // 4. 渲染逻辑：使用条件内容而不是条件返回
  return (
    <div ref={contentRef} className={cn("w-full pt-4 pb-8", className)}>
      {shouldShowEditor ? (
        <ReportEditor
          content={message?.content ?? ""}
          onMarkdownChange={handleMarkdownChange}
        />
      ) : (
        <>
          {!message ? (
            <div>Loading...</div>
          ) : !hasContent ? (
            <div>No content available</div>
          ) : (
            <>
              <Markdown
                animated={false} // 禁用动画以提升性能
                checkLinkCredibility={!isLargeContent} // 大文档时禁用链接检查以提升性能
              >
                {message.content}
              </Markdown>
              {message.isStreaming && <LoadingAnimation className="my-12" />}
            </>
          )}
        </>
      )}
    </div>
  );
});
